#!/usr/bin/env python3
"""
Comprehensive test for the complete refactored DeSmuME system
"""
import os
import sys
import time
import sqlite3
import threading
from unittest.mock import patch

def test_imports():
    """Test that all required modules can be imported"""
    try:
        from desmume_controller import DeSmuMEController
        from desmume.emulator import <PERSON>Smu<PERSON>
        from desmume.controls import keymask, Keys
        print("✓ All DeSmuME modules imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_configuration_loading():
    """Test that configuration can be loaded from main.py"""
    try:
        from main import CONFIG
        
        # Check that DeSmuME-specific configs are present
        expected_keys = [
            'database_check_interval',
            'state_save_interval', 
            'key_press_duration',
            'rom_path'
        ]
        
        for key in expected_keys:
            if key not in CONFIG:
                print(f"✗ Missing config key: {key}")
                return False
        
        print("✓ Configuration loaded successfully from main.py")
        return True
    except ImportError as e:
        print(f"✗ Could not import CONFIG from main.py: {e}")
        return False

def test_database_integration():
    """Test database integration without running emulator"""
    try:
        from desmume_controller import DeSmuMEController
        
        controller = DeSmuMEController()
        
        # Test database connection
        controller.get_last_move()
        print(f"✓ Database connection works, last move ID: {controller.last_move_id}")
        
        # Test pending move functionality
        controller.pending_move = "test_move"
        move = controller.get_pending_move()
        if move == "test_move":
            print("✓ Pending move system works")
        else:
            print("✗ Pending move system failed")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Database integration test failed: {e}")
        return False

def test_input_mapping():
    """Test that all input mappings are correct"""
    try:
        from desmume_controller import DeSmuMEController
        from desmume.controls import Keys
        
        controller = DeSmuMEController()
        
        # Expected mappings
        expected_mappings = {
            'a': Keys.KEY_A,
            'b': Keys.KEY_B,
            'x': Keys.KEY_X,
            'y': Keys.KEY_Y,
            'l': Keys.KEY_L,
            'r': Keys.KEY_R,
            'up': Keys.KEY_UP,
            'down': Keys.KEY_DOWN,
            'left': Keys.KEY_LEFT,
            'right': Keys.KEY_RIGHT,
            'select': Keys.KEY_SELECT,
            'start': Keys.KEY_START,
        }
        
        for move, expected_key in expected_mappings.items():
            if move not in controller.key_map:
                print(f"✗ Missing key mapping for: {move}")
                return False
            if controller.key_map[move] != expected_key:
                print(f"✗ Incorrect key mapping for {move}: expected {expected_key}, got {controller.key_map[move]}")
                return False
        
        print("✓ All input mappings are correct")
        return True
    except Exception as e:
        print(f"✗ Input mapping test failed: {e}")
        return False

def test_emulator_initialization():
    """Test emulator initialization with ROM"""
    try:
        from desmume_controller import DeSmuMEController
        
        config = {
            'auto_load_state': False,
            'audio_enabled': False,  # Disable for testing
            'framerate_limit': 60,
        }
        
        controller = DeSmuMEController(rom_path='phg.nds', config=config)
        
        if controller.initialize_emulator():
            print("✓ Emulator initialization successful")
            
            # Test that emulator is properly configured
            if controller.emu:
                print("✓ Emulator instance created")
                
                # Test basic emulator functions
                running = controller.emu.is_running()
                print(f"✓ Emulator running state: {running}")
                
                controller.cleanup()
                return True
            else:
                print("✗ Emulator instance not created")
                return False
        else:
            print("✗ Emulator initialization failed")
            return False
            
    except Exception as e:
        print(f"✗ Emulator initialization test failed: {e}")
        return False

def test_state_management():
    """Test state save/load functionality"""
    try:
        from desmume_controller import DeSmuMEController
        
        config = {
            'auto_load_state': False,
            'audio_enabled': False,
            'state_file': 'test_complete_system.dst',
        }
        
        controller = DeSmuMEController(rom_path='phg.nds', config=config)
        
        if not controller.initialize_emulator():
            print("✗ Could not initialize emulator for state test")
            return False
        
        # Test state save
        controller.save_state()
        
        if os.path.exists(config['state_file']):
            print("✓ State save functionality works")
            
            # Test state load
            controller.load_state_on_startup()
            print("✓ State load functionality works")
            
            # Clean up
            controller.cleanup()
            os.remove(config['state_file'])
            return True
        else:
            print("✗ State file was not created")
            controller.cleanup()
            return False
            
    except Exception as e:
        print(f"✗ State management test failed: {e}")
        return False

def test_startup_script():
    """Test that the startup script can be imported and configured"""
    try:
        # Test import
        import start_desmume
        print("✓ Startup script imports successfully")
        
        # Test that it has the main function
        if hasattr(start_desmume, 'main'):
            print("✓ Startup script has main function")
            return True
        else:
            print("✗ Startup script missing main function")
            return False
            
    except ImportError as e:
        print(f"✗ Could not import startup script: {e}")
        return False

def test_gui_updates():
    """Test that GUI has been updated for DeSmuME"""
    try:
        from gui import PumpPokemonGUI
        
        # Create a mock chat handler
        class MockChatHandler:
            def __init__(self):
                self.config = {'gui_update_interval': 0.1}
            
            def get_stats(self):
                return {
                    'mode': 'democracy',
                    'current_votes': {},
                    'user_count': 0,
                    'session_time': '00:00:00',
                    'persistent_session_time': '00:00:00',
                    'move_timer': 0,
                    'mode_timer': 0,
                    'mode_swap_interval': 60,
                    'total_messages': 0,
                    'last_moves': [],
                    'all_messages': [],
                    'parsed_messages': []
                }
        
        mock_handler = MockChatHandler()
        
        # Test GUI creation (don't actually run it)
        gui = PumpPokemonGUI(mock_handler)
        
        # Check title
        if "DeSmuME" in gui.root.title():
            print("✓ GUI title updated for DeSmuME")
            
            # Clean up
            gui.root.destroy()
            return True
        else:
            print("✗ GUI title not updated for DeSmuME")
            gui.root.destroy()
            return False
            
    except Exception as e:
        print(f"✗ GUI test failed: {e}")
        return False

def main():
    """Run all comprehensive system tests"""
    print("Testing Complete Refactored DeSmuME System")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_configuration_loading,
        test_input_mapping,
        test_database_integration,
        test_startup_script,
        test_gui_updates,
        test_emulator_initialization,
        test_state_management,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
        print()
    
    print("=" * 60)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ ALL TESTS PASSED! The refactored DeSmuME system is ready!")
        print()
        print("The system now uses py-desmume instead of MelonDS + pyautogui:")
        print("  ✓ Direct emulator control (no external process)")
        print("  ✓ Optimized performance and framerate")
        print("  ✓ Proper state management")
        print("  ✓ Seamless database integration")
        print("  ✓ All input controls working")
        print()
        print("To run the system:")
        print("  python start_desmume.py")
        print("  or")
        print("  python main.py")
        return True
    else:
        print("✗ Some tests failed. Please check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

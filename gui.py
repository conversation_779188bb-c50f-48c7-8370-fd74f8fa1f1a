import tkinter as tk
from tkinter import ttk
import threading
import time
from datetime import datetime, timedelta

class ScrollingChatFrame:
    def __init__(self, parent, title, max_items=10, bg_color='green'):
        self.max_items = max_items
        self.item_labels = []  # Store the actual labels for text updates
        self.item_texts = []   # Store current text content

        # Main frame
        self.frame = tk.Frame(parent, bg=bg_color, relief='solid', bd=1)

        # Title
        title_label = tk.Label(
            self.frame,
            text=title,
            font=('Arial', 12, 'bold'),
            bg=bg_color,
            fg='white'
        )
        title_label.pack(pady=5)

        # Scrollable content frame
        self.content_frame = tk.Frame(self.frame, bg=bg_color)
        self.content_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Pre-create all label widgets to prevent flickering
        for i in range(max_items):
            item_frame = tk.Frame(self.content_frame, bg='green')
            item_label = tk.Label(
                item_frame,
                text="",  # Start empty
                font=('Arial', 12, 'bold'),
                bg='green',
                fg='white',
                anchor='w',
                justify='left',
                width=25,
                wraplength=200
            )
            item_label.pack(fill='x', padx=3, pady=2)
            item_frame.pack(fill='x', pady=1)
            self.item_labels.append(item_label)
            self.item_texts.append("")

    def add_item(self, text, animate=True):
        # Shift all existing texts down and add new one at top
        self.item_texts.insert(0, text)
        
        # Keep only max_items
        if len(self.item_texts) > self.max_items:
            self.item_texts = self.item_texts[:self.max_items]
        
        # Update all label texts using config() to prevent flickering
        for i, label in enumerate(self.item_labels):
            if i < len(self.item_texts):
                label.config(text=self.item_texts[i])
            else:
                label.config(text="")  # Clear unused labels

    def clear(self):
        # Clear all text content using config() instead of destroying widgets
        self.item_texts = []
        for label in self.item_labels:
            label.config(text="")

class PumpPokemonGUI:
    def __init__(self, chat_handler):
        self.chat_handler = chat_handler
        self.config = getattr(chat_handler, 'config', {'gui_update_interval': 0.1})
        self.root = tk.Tk()
        self.root.title("Pump Plays Pokemon (MelonDS)")
        self.root.configure(bg='green')
        self.root.geometry("900x600")  # Wider window for better pane usage

        # Track last states to prevent flickering
        self.last_mode = ""
        self.last_votes = {}
        self.last_user_count = 0
        self.last_move_timer = 0
        self.last_session_time = ""
        self.last_persistent_time = ""
        self.last_messages_count = 0
        self.last_moves = []
        self.last_parsed_messages = []
        self.last_bar_votes = {}  # Track last votes to prevent unnecessary updates

        self.setup_ui()

        # Start update thread
        self.running = True
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()

    def setup_ui(self):
        # Create main frame
        main_frame = tk.Frame(self.root, bg='green', padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)

        # Title
        title_label = tk.Label(
            main_frame,
            text="PUMP PLAYS POKEMON (MelonDS)",
            font=('Arial', 24, 'bold'),
            bg='green',
            fg='white'
        )
        title_label.pack(pady=(0, 20))

        # Mode display
        self.mode_label = tk.Label(
            main_frame,
            text="MODE: DEMOCRACY",
            font=('Arial', 16, 'bold'),
            bg='green',
            fg='white'
        )
        self.mode_label.pack(pady=5)
        
        # Progress bar (without mode swap timer label)
        self.mode_progress = ttk.Progressbar(
            main_frame,
            length=400,
            mode='determinate',
            style='White.Horizontal.TProgressbar'
        )
        self.mode_progress.pack(pady=5)

        # Configure white progressbar style
        style = ttk.Style()
        style.theme_use('default')
        style.configure('White.Horizontal.TProgressbar',
                       background='white',
                       troughcolor='green',
                       borderwidth=1,
                       lightcolor='white',
                       darkcolor='white')
        
        # Stats frame
        stats_frame = tk.Frame(main_frame, bg='green')
        stats_frame.pack(fill='x', pady=10)
        
        # Left column
        left_col = tk.Frame(stats_frame, bg='green')
        left_col.pack(side='left', fill='both', expand=True)
        
        self.votes_label = tk.Label(
            left_col,
            text="CURRENT VOTES: 0",
            font=('Arial', 14),
            bg='green',
            fg='white',
            anchor='w'
        )
        self.votes_label.pack(anchor='w')
        
        self.users_label = tk.Label(
            left_col,
            text="ACTIVE USERS: 0",
            font=('Arial', 14),
            bg='green',
            fg='white',
            anchor='w'
        )
        self.users_label.pack(anchor='w')
        
        self.timer_label = tk.Label(
            left_col,
            text="MOVE TIMER: 0s",
            font=('Arial', 14),
            bg='green',
            fg='white',
            anchor='w'
        )
        self.timer_label.pack(anchor='w')
        
        # Right column  
        right_col = tk.Frame(stats_frame, bg='green')
        right_col.pack(side='right', fill='both', expand=True)
        
        # Add persistent session timer
        self.persistent_timer_label = tk.Label(
            right_col,
            text="TOTAL TIME: 00:00:00",
            font=('Arial', 14, 'bold'),
            bg='green',
            fg='white',
            anchor='e'
        )
        self.persistent_timer_label.pack(anchor='e')

        self.session_label = tk.Label(
            right_col,
            text="SESSION: 00:00:00",
            font=('Arial', 14),
            bg='green',
            fg='white',
            anchor='e'
        )
        self.session_label.pack(anchor='e')

        self.messages_label = tk.Label(
            right_col,
            text="MESSAGES: 0",
            font=('Arial', 14),
            bg='green',
            fg='white',
            anchor='e'
        )
        self.messages_label.pack(anchor='e')
        
        # Three pane section
        panes_section = tk.Frame(main_frame, bg='green')
        panes_section.pack(fill='both', expand=True, pady=5)

        # Left pane - Move executions chat
        left_pane = tk.Frame(panes_section, bg='green', width=220)
        left_pane.pack(side='left', fill='both', expand=True, padx=(0, 5))
        left_pane.pack_propagate(False)  # Prevent size changes

        self.moves_chat = ScrollingChatFrame(
            left_pane,
            "LAST 10 MOVES",
            max_items=15,
            bg_color='green'
        )
        self.moves_chat.frame.pack(fill='both', expand=True)

        # Middle pane - Voting bar chart
        middle_pane = tk.Frame(panes_section, bg='green', width=220)
        middle_pane.pack(side='left', fill='both', expand=True, padx=5)
        middle_pane.pack_propagate(False)  # Prevent size changes
        
        # Vote breakdown title in middle pane
        votes_title = tk.Label(
            middle_pane,
            text="CURRENT VOTES",
            font=('Arial', 12, 'bold'),
            bg='green',
            fg='white'
        )
        votes_title.pack(pady=(10, 5))
        
        # Canvas for bar charts in middle pane
        self.bars_canvas = tk.Canvas(middle_pane, bg='green', highlightthickness=0, height=400)
        self.bars_canvas.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Pre-create anarchy mode label (initially hidden) - overlay on canvas
        self.anarchy_label = tk.Label(
            middle_pane,
            text="ANARCHY MODE!",
            font=('Arial', 12, 'bold'),
            bg='green',
            fg='white'
        )
        # Don't pack it initially - we'll pack/pack_forget to show/hide
        
        # Right pane - User messages chat
        right_pane = tk.Frame(panes_section, bg='green', width=220)
        right_pane.pack(side='left', fill='both', expand=True, padx=(5, 0))
        right_pane.pack_propagate(False)  # Prevent size changes

        self.messages_chat = ScrollingChatFrame(
            right_pane,
            "USER COMMANDS",
            max_items=15,
            bg_color='green'
        )
        self.messages_chat.frame.pack(fill='both', expand=True)
        
        # Last move
        self.last_move_label = tk.Label(
            main_frame,
            text="LAST MOVE: NONE",
            font=('Arial', 18, 'bold'),
            bg='green',
            fg='white'
        )
        self.last_move_label.pack(pady=20)

        # Start update thread
        self.running = True
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()
        
    def update_display(self):
        stats = self.chat_handler.get_stats()

        # Update mode (only if changed to prevent flickering)
        if stats['mode'] != self.last_mode:
            self.mode_label.config(text=f"MODE: {stats['mode'].upper()}")
            self.last_mode = stats['mode']

        # Update mode timer progress bar
        mode_progress = (stats['mode_timer'] / stats['mode_swap_interval']) * 100
        self.mode_progress['value'] = mode_progress

        # Update stats (only if changed)
        total_votes = sum(stats['current_votes'].values())
        if stats['current_votes'] != self.last_votes:
            self.votes_label.config(text=f"CURRENT VOTES: {total_votes}")
            self.last_votes = stats['current_votes'].copy()

        if stats['user_count'] != self.last_user_count:
            self.users_label.config(text=f"ACTIVE USERS: {stats['user_count']}")
            self.last_user_count = stats['user_count']

        if stats['move_timer'] != self.last_move_timer:
            self.timer_label.config(text=f"MOVE TIMER: {stats['move_timer']}s")
            self.last_move_timer = stats['move_timer']

        if stats['session_time'] != self.last_session_time:
            self.session_label.config(text=f"SESSION: {stats['session_time']}")
            self.last_session_time = stats['session_time']

        if stats['persistent_session_time'] != self.last_persistent_time:
            self.persistent_timer_label.config(text=f"TOTAL TIME: {stats['persistent_session_time']}")
            self.last_persistent_time = stats['persistent_session_time']

        if stats['total_messages'] != self.last_messages_count:
            self.messages_label.config(text=f"MESSAGES: {stats['total_messages']}")
            self.last_messages_count = stats['total_messages']

        # Update chat displays
        self.update_moves_chat(stats['last_moves'])
        self.update_messages_chat(stats['parsed_messages'])
        
        # Update vote breakdown
        self.update_vote_bars(stats['current_votes'], stats['mode'])

        # Update last move
        if stats['last_moves']:
            last_move = stats['last_moves'][-1]
            self.last_move_label.config(text=f"LAST MOVE: {last_move['move']}")
        else:
            self.last_move_label.config(text="LAST MOVE: NONE")
    


    def update_moves_chat(self, moves):
        # Only update if moves have changed to prevent flickering
        if len(moves) != len(self.last_moves) or (moves and self.last_moves and moves[-1] != self.last_moves[-1]):
            # Find new moves (ones not in last_moves)
            new_moves = []
            if len(moves) > len(self.last_moves):
                new_moves = moves[len(self.last_moves):]
            elif moves != self.last_moves:
                # If moves list changed but not just appended, refresh all
                new_moves = moves

            # Add new moves to chat without timestamps
            for move in new_moves:
                if move['mode'] == 'democracy':
                    text = f"{move['move']} ({move['votes']} votes)"
                else:
                    user_display = move['user'][:15] + '...' if len(move['user']) > 15 else move['user']
                    text = f"{move['move']} by {user_display}"

                self.moves_chat.add_item(text)

            self.last_moves = moves.copy()

    def update_messages_chat(self, parsed_messages):
        # Only update if messages have changed to prevent flickering
        if len(parsed_messages) != len(self.last_parsed_messages) or (parsed_messages and self.last_parsed_messages and parsed_messages[-1] != self.last_parsed_messages[-1]):
            # Find new messages
            new_messages = []
            if len(parsed_messages) > len(self.last_parsed_messages):
                new_messages = parsed_messages[len(self.last_parsed_messages):]
            elif parsed_messages != self.last_parsed_messages:
                # If messages list changed but not just appended, refresh all
                new_messages = parsed_messages

            # Add new messages to chat without timestamps
            for msg in new_messages:
                user_display = msg['username'][:12] + '...' if len(msg['username']) > 12 else msg['username']
                text = f"{user_display}: {msg['move']}"
                self.messages_chat.add_item(text)

            self.last_parsed_messages = parsed_messages.copy()
    
    def update_vote_bars(self, current_votes, mode):
        # Only update if votes actually changed
        if current_votes == self.last_bar_votes and mode != 'anarchy':
            return
            
        self.last_bar_votes = current_votes.copy()
        
        if mode == 'anarchy':
            # Clear canvas and show anarchy label
            self.bars_canvas.delete("all")
            self.anarchy_label.pack()
            return
        else:
            # Hide anarchy label if it was shown
            self.anarchy_label.pack_forget()
        
        # Clear canvas
        self.bars_canvas.delete("all")
        
        if not current_votes:
            return
        
        # Sort moves by vote count (highest first)
        sorted_moves = sorted(current_votes.items(), key=lambda x: x[1], reverse=True)
        
        # Calculate max votes for scaling
        max_votes = max(current_votes.values()) if current_votes else 1
        max_bar_width = 150
        
        # Draw bars on canvas
        y_position = 20
        bar_height = 20
        spacing = 30
        
        for move, count in sorted_moves[:10]:  # Limit to 10 bars to fit in view
            # Calculate bar width
            bar_width = int((count / max_votes) * max_bar_width) if max_votes > 0 else 0
            if bar_width < 10 and count > 0:
                bar_width = 10
            
            # Draw move label
            self.bars_canvas.create_text(
                10, y_position + bar_height//2,
                text=f"{move.upper()}:",
                fill='white',
                font=('Arial', 10, 'bold'),
                anchor='w'
            )
            
            # Draw bar background
            bg_x1 = 80
            bg_y1 = y_position
            bg_x2 = bg_x1 + max_bar_width
            bg_y2 = y_position + bar_height
            
            self.bars_canvas.create_rectangle(
                bg_x1, bg_y1, bg_x2, bg_y2,
                fill='darkgreen',
                outline='darkgreen'
            )
            
            # Draw actual bar
            if bar_width > 0:
                self.bars_canvas.create_rectangle(
                    bg_x1, bg_y1, bg_x1 + bar_width, bg_y2,
                    fill='white',
                    outline='white'
                )
            
            # Draw vote count
            self.bars_canvas.create_text(
                bg_x2 + 10, y_position + bar_height//2,
                text=str(count),
                fill='white',
                font=('Arial', 10, 'bold'),
                anchor='w'
            )
            
            y_position += spacing
    
    def update_loop(self):
        while self.running:
            try:
                self.root.after(0, self.update_display)
                time.sleep(self.config.get('gui_update_interval', 0.1))
            except:
                break
    
    def run(self):
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        self.running = False
        self.root.quit()

#!/usr/bin/env python3
"""
Test script for DeSmuME state management functionality
"""
import os
import time
import sys
from desmume_controller import DeSmuMEController

def test_state_save_load():
    """Test state save and load functionality"""
    try:
        config = {
            'auto_load_state': False,  # Don't auto-load for testing
            'audio_enabled': False,
            'state_file': 'test_state_management.dst',
        }
        
        controller = DeSmuMEController(rom_path='phg.nds', config=config)
        
        if not controller.initialize_emulator():
            print("✗ Failed to initialize emulator")
            return False
        
        print("✓ Emulator initialized")
        
        # Test state save
        try:
            controller.save_state()
            print("✓ State save successful")
            
            # Check if state file was created
            if os.path.exists(controller.config['state_file']):
                print("✓ State file created successfully")
                file_size = os.path.getsize(controller.config['state_file'])
                print(f"✓ State file size: {file_size} bytes")
            else:
                print("✗ State file was not created")
                return False
                
        except Exception as e:
            print(f"✗ State save failed: {e}")
            return False
        
        # Test state load
        try:
            controller.load_state_on_startup()
            print("✓ State load successful")
        except Exception as e:
            print(f"✗ State load failed: {e}")
            return False
        
        # Clean up
        controller.cleanup()
        
        # Remove test state file
        if os.path.exists(controller.config['state_file']):
            os.remove(controller.config['state_file'])
            print("✓ Test state file cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ State management test failed: {e}")
        return False

def test_auto_load_state():
    """Test automatic state loading on startup"""
    try:
        # First, create a state file
        config = {
            'auto_load_state': False,
            'audio_enabled': False,
            'state_file': 'test_auto_load.dst',
        }
        
        controller = DeSmuMEController(rom_path='phg.nds', config=config)
        
        if not controller.initialize_emulator():
            print("✗ Failed to initialize emulator for auto-load test")
            return False
        
        # Save a state
        controller.save_state()
        controller.cleanup()
        
        # Now test auto-loading
        config['auto_load_state'] = True
        controller2 = DeSmuMEController(rom_path='phg.nds', config=config)
        
        if controller2.initialize_emulator():
            print("✓ Auto-load state test successful")
            controller2.cleanup()
            
            # Clean up test file
            if os.path.exists(config['state_file']):
                os.remove(config['state_file'])
                
            return True
        else:
            print("✗ Auto-load state test failed")
            return False
            
    except Exception as e:
        print(f"✗ Auto-load state test failed: {e}")
        return False

def test_periodic_save_config():
    """Test that periodic save configuration is properly set up"""
    try:
        config = {
            'state_save_interval': 30,  # 30 seconds for testing
            'auto_load_state': False,
            'audio_enabled': False,
        }
        
        controller = DeSmuMEController(rom_path='phg.nds', config=config)
        
        # Check that the config is properly stored
        if controller.config['state_save_interval'] == 30:
            print("✓ Periodic save interval configured correctly")
            return True
        else:
            print("✗ Periodic save interval not configured correctly")
            return False
            
    except Exception as e:
        print(f"✗ Periodic save config test failed: {e}")
        return False

def main():
    """Run all state management tests"""
    print("Testing DeSmuME State Management...")
    print("=" * 50)
    
    tests = [
        test_periodic_save_config,
        test_state_save_load,
        test_auto_load_state,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
        print()
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All state management tests passed!")
        return True
    else:
        print("✗ Some state management tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

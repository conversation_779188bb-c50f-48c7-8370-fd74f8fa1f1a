import asyncio
import threading
from chat_handler import <PERSON>umpChatHandler
from gui import <PERSON>ump<PERSON>okemonGUI

# Configuration - All timers and cooldowns
CONFIG = {
    'room_id': 'EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump',
    'democracy_vote_duration_seconds': 8.0,  # How long each democratic vote lasts
    'move_interval_seconds_anarchy': 0.25,  # Time between moves in anarchy mode (faster)
    'democracy_duration_minutes': 0.5,  # Democracy mode duration in minutes
    'anarchy_duration_minutes': 2.0,  # Anarchy mode duration in minutes
    'gui_update_interval': 0.02,  # GUI update interval in seconds (20 FPS)
    'websocket_sleep': 0.1,  # Sleep time in websocket error recovery

    # MelonDS settings (using pyautogui)
    'database_check_interval': 0.1,  # Check database every 100ms
    'state_save_interval': 60,  # Save state every 60 seconds
    'key_press_duration': 0.1,  # Hold keys for 100ms
    'auto_launch_melonds': True,  # Auto-launch MelonDS
    'melonds_path': './melonDS-x86_64.AppImage',  # Path to MelonDS
    'rom_path': 'phg.nds'  # ROM file path
}


def run_gui(chat_handler):
    gui = PumpPokemonGUI(chat_handler)
    gui.run()

async def main():
    # Initialize chat handler
    chat_handler = PumpChatHandler(CONFIG['room_id'], CONFIG)
    
    # Start GUI in separate thread
    gui_thread = threading.Thread(
        target=run_gui, 
        args=(chat_handler,),
        daemon=True
    )
    gui_thread.start()
    
    print("Starting Pump Plays Pokemon (MelonDS)...")
    print(f"Mode: {chat_handler.mode}")
    print("Supported controls: A, B, X, Y, L, R, Start, Select, Up, Down, Left, Right")
    print("Make sure MelonDS is running with your ROM loaded!")
    
    # Connect to chat and start listening
    while True:
        try:
            await chat_handler.connect_and_listen()
        except KeyboardInterrupt:
            print("Shutting down...")
            break
            
        except Exception:
            await asyncio.sleep(CONFIG['websocket_sleep'])
            continue

if __name__ == "__main__":
    asyncio.run(main())
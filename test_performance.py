#!/usr/bin/env python3
"""
Test script for DeSmuME performance optimizations
"""
import time
import sys
from desmume_controller import DeSmuMEController

def test_performance_config():
    """Test that performance configurations are properly applied"""
    try:
        config = {
            'framerate_limit': 30,  # Lower for testing
            'audio_enabled': True,
            'audio_volume': 25,
            'skip_frame_limit': 1,  # Skip every other frame
            'auto_load_state': False,
        }
        
        controller = DeSmuMEController(rom_path='phg.nds', config=config)
        
        # Check configuration
        if controller.config['framerate_limit'] == 30:
            print("✓ Framerate limit configured correctly")
        else:
            print("✗ Framerate limit not configured correctly")
            return False
            
        if controller.config['audio_volume'] == 25:
            print("✓ Audio volume configured correctly")
        else:
            print("✗ Audio volume not configured correctly")
            return False
            
        if controller.config['skip_frame_limit'] == 1:
            print("✓ Frame skip configured correctly")
        else:
            print("✗ Frame skip not configured correctly")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Performance config test failed: {e}")
        return False

def test_emulator_performance():
    """Test actual emulator performance with optimizations"""
    try:
        config = {
            'framerate_limit': 60,
            'audio_enabled': False,  # Disable for performance testing
            'audio_volume': 0,
            'skip_frame_limit': 0,  # No frame skipping
            'auto_load_state': False,
        }
        
        controller = DeSmuMEController(rom_path='phg.nds', config=config)
        
        if not controller.initialize_emulator():
            print("✗ Failed to initialize emulator for performance test")
            return False
        
        print("✓ Emulator initialized with performance settings")
        
        # Test a few cycles to ensure smooth operation
        print("Testing emulator cycles...")
        start_time = time.time()
        
        for i in range(10):
            controller.emu.cycle(with_joystick=False)
            
        elapsed = time.time() - start_time
        fps = 10 / elapsed if elapsed > 0 else 0
        
        print(f"✓ Completed 10 cycles in {elapsed:.3f} seconds")
        print(f"✓ Effective FPS: {fps:.1f}")
        
        if fps > 30:  # Should be able to maintain at least 30 FPS
            print("✓ Performance test passed")
            result = True
        else:
            print("⚠ Performance may be suboptimal")
            result = True  # Still pass, as this depends on system
        
        controller.cleanup()
        return result
        
    except Exception as e:
        print(f"✗ Performance test failed: {e}")
        return False

def test_audio_configuration():
    """Test audio configuration options"""
    try:
        # Test with audio disabled
        config1 = {
            'audio_enabled': False,
            'auto_load_state': False,
        }
        
        controller1 = DeSmuMEController(rom_path='phg.nds', config=config1)
        
        if controller1.initialize_emulator():
            print("✓ Audio disabled configuration works")
            controller1.cleanup()
        else:
            print("✗ Audio disabled configuration failed")
            return False
        
        # Test with audio enabled and custom volume
        config2 = {
            'audio_enabled': True,
            'audio_volume': 30,
            'auto_load_state': False,
        }
        
        controller2 = DeSmuMEController(rom_path='phg.nds', config=config2)
        
        if controller2.initialize_emulator():
            print("✓ Audio enabled with custom volume works")
            controller2.cleanup()
            return True
        else:
            print("✗ Audio enabled configuration failed")
            return False
        
    except Exception as e:
        print(f"✗ Audio configuration test failed: {e}")
        return False

def main():
    """Run all performance tests"""
    print("Testing DeSmuME Performance Optimizations...")
    print("=" * 50)
    
    tests = [
        test_performance_config,
        test_audio_configuration,
        test_emulator_performance,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
        print()
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All performance tests passed!")
        return True
    else:
        print("✗ Some performance tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

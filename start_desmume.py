#!/usr/bin/env python3
"""
DeSmuME Startup Script for Pump Plays Pokemon

This script replaces start_melonds.py and provides the same functionality
but using the new py-desmume controller instead of external MelonDS + pyautogui.
"""
import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from desmume_controller import DeSmuMEController


def main():
    """Main entry point for the DeSmuME controller."""
    print("=" * 60)
    print("PUMP PLAYS POKEMON - DeSmuME Controller")
    print("=" * 60)
    print()
    
    # Import CONFIG from main.py if available
    try:
        from main import CONFIG
        
        # Create DeSmuME-specific config
        desmume_config = CONFIG.copy()
        desmume_config.update({
            'database_check_interval': 0.1,  # Check DB every 100ms
            'state_save_interval': 60,  # Save state every 60 seconds
            'key_press_duration': 0.1,  # Hold keys for 100ms
            'auto_load_state': True,  # Auto-load state on startup
            'framerate_limit': 60,  # Target 60 FPS for smooth gameplay
            'audio_enabled': True,  # Enable audio
            'audio_volume': 50,  # 50% volume to avoid audio issues
            'state_file': 'desmume_state.dst',  # State file
            'skip_frame_limit': 0,  # No frame skipping by default
            'cpu_mode': 'jit',  # Use JIT for better performance
            'threaded_3d': True,  # Enable threaded 3D rendering
            'opengl_enabled': True,  # Use OpenGL if available
        })

        rom_path = desmume_config.get('rom_path', 'phg.nds')
        
        print(f"Configuration loaded from main.py")
        print(f"ROM path: {rom_path}")
        print(f"Database check interval: {desmume_config['database_check_interval']}s")
        print(f"State save interval: {desmume_config['state_save_interval']}s")
        print(f"Framerate limit: {desmume_config['framerate_limit']} FPS")
        print(f"Audio enabled: {desmume_config['audio_enabled']}")
        print()
        
    except ImportError:
        print("Could not import CONFIG from main.py, using default configuration")
        
        # Default configuration
        desmume_config = {
            'database_check_interval': 0.1,
            'state_save_interval': 60,
            'key_press_duration': 0.1,
            'auto_load_state': True,
            'framerate_limit': 60,
            'audio_enabled': True,
            'audio_volume': 50,
            'state_file': 'desmume_state.dst',
            'skip_frame_limit': 0,
            'cpu_mode': 'jit',
            'threaded_3d': True,
            'opengl_enabled': True,
        }
        
        rom_path = 'phg.nds'
        print(f"Using default ROM path: {rom_path}")
        print()
    
    # Check if ROM file exists
    if not os.path.exists(rom_path):
        print(f"ERROR: ROM file not found at '{rom_path}'")
        print("Please ensure the ROM file is in the correct location.")
        print()
        return 1
    
    # Create and run the controller
    try:
        print("Initializing DeSmuME controller...")
        controller = DeSmuMEController(rom_path=rom_path, config=desmume_config)
        
        print("Starting emulation...")
        print("The emulator will:")
        print("  - Load the ROM automatically")
        print("  - Load saved state if available")
        print("  - Poll the database for new moves")
        print("  - Save state periodically")
        print()
        print("Press Ctrl+C to stop the emulator")
        print("=" * 60)
        print()
        
        # Run the controller (this will block until stopped)
        controller.run()
        
        return 0
        
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
        return 0
    except Exception as e:
        print(f"ERROR: Failed to run DeSmuME controller: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

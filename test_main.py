import asyncio
import threading
from test_chat_handler import Test<PERSON><PERSON><PERSON>hatHandler
from gui import Pump<PERSON><PERSON><PERSON>GUI

# Configuration for testing
TEST_CONFIG = {
    'room_id': 'test_room',
    'move_interval_seconds': 3,  # Time between moves in democracy mode
    'move_interval_seconds_anarchy': 1.75,  # Time between moves in democracy mode
    'mode_swap_minutes': 0.5  # Faster mode swaps for testing (30 seconds)
}

def run_gui(chat_handler):
    gui = PumpPokemonGUI(chat_handler)
    gui.run()

async def main():
    # Initialize test chat handler
    chat_handler = TestPumpChatHandler(TEST_CONFIG['room_id'], TEST_CONFIG)
    
    # Start GUI in separate thread
    gui_thread = threading.Thread(
        target=run_gui, 
        args=(chat_handler,),
        daemon=True
    )
    gui_thread.start()
    
    print("Starting Test Pump Plays Pokemon...")
    print(f"Mode: {chat_handler.mode}")
    print(f"Move interval: {TEST_CONFIG['move_interval_seconds']}s")
    print(f"Mode swap interval: {TEST_CONFIG['mode_swap_minutes']} minutes")
    
    # Connect to test chat and start generating messages
    try:
        await chat_handler.connect_and_listen()
    except KeyboardInterrupt:
        print("Shutting down...")

if __name__ == "__main__":
    asyncio.run(main())
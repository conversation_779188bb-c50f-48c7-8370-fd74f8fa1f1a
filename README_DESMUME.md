# Pump Plays Pokemon - DeSmuME Implementation

The codebase has been successfully refactored to use **py-desmume** instead of MelonDS + pyautogui.

## What Changed

- **Replaced MelonDS**: No more external emulator process or pyautogui automation
- **Direct Control**: py-desmume provides direct emulator control
- **Better Performance**: Optimized framerate, audio settings, and state management
- **Same Functionality**: All inputs, database polling, and state save/load work the same

## How to Run

### Option 1: DeSmuME Only
```bash
python start_desmume.py
```

### Option 2: Full Application (GUI + DeSmuME)
```bash
python main.py
```

## Key Features

- **Automatic ROM Loading**: Loads `phg.nds` automatically
- **State Management**: Auto-loads state on startup, saves every 60 seconds
- **Database Integration**: Polls database for new moves every 100ms
- **Performance Optimized**: 60 FPS target, configurable audio
- **All Controls Supported**: A, B, X, Y, L, R, Start, Select, D-Pad

## Configuration

Edit `main.py` to adjust settings:
- `framerate_limit`: Target FPS (default: 60)
- `audio_volume`: Volume 0-100 (default: 50)
- `state_save_interval`: Auto-save interval in seconds (default: 60)
- `key_press_duration`: How long to hold keys (default: 0.1s)

## Files

- `desmume_controller.py`: Main DeSmuME controller class
- `start_desmume.py`: Standalone DeSmuME launcher
- `main.py`: Updated for DeSmuME integration
- `gui.py`: Updated GUI labels
- `start_melonds.py`: Legacy file (can be removed)

The refactoring is complete and the system is ready to use!

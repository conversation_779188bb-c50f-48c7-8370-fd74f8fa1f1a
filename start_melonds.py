#!/usr/bin/env python3
import os
import time
import sqlite3
import threading
import subprocess
import pyautogui
import psutil

class MelonDSController:
    def __init__(self, rom_path='phg.nds', config=None):
        self.rom_path = rom_path
        self.last_move_id = 0  # Track last processed move from database
        self.config = config or {
            'database_check_interval': 0.1,  # Check database every 100ms
            'state_save_interval': 60,  # Save state every 60 seconds
            'key_press_duration': 0.1,  # How long to hold keys (100ms)
            'auto_launch_melonds': True,  # Auto-launch MelonDS
            'melonds_path': './melonDS-x86_64.AppImage',  # Path to MelonDS
        }
        
        # Threading for database polling to prevent lag
        self.db_check_thread = None
        self.state_save_thread = None
        self.running = False
        self.pending_move = None
        self.move_lock = threading.Lock()
        
        # Configure pyautogui
        pyautogui.FAILSAFE = True  # Move mouse to corner to stop
        pyautogui.PAUSE = 0.05  # Slightly longer pause between actions

        # Store MelonDS process for window focusing
        self.melonds_process = None
        
        self.get_last_move()
        
    def launch_melonds(self):
        """Launch MelonDS with the specified ROM and window geometry"""
        if not self.config.get('auto_launch_melonds', True):
            return False

        melonds_path = self.config.get('melonds_path', './melonDS-x86_64.AppImage')

        if not os.path.exists(melonds_path):
            print(f"MelonDS not found at: {melonds_path}")
            return False

        if not os.path.exists(self.rom_path):
            print(f"ROM not found at: {self.rom_path}")
            return False

        try:
            print(f"Launching MelonDS with ROM: {self.rom_path}")
            # Launch MelonDS with specific window geometry and ROM
            cmd = [melonds_path, '--qwindowgeometry', '500x500+250+250', self.rom_path]
            self.melonds_process = subprocess.Popen(cmd)
            print("MelonDS launched successfully")
            print("Waiting 5 seconds for MelonDS to start...")
            time.sleep(5)  # Give MelonDS more time to start
            return True
        except Exception as e:
            print(f"Error launching MelonDS: {e}")
            return False

    def initialize_melonds(self):
        """Initialize melonDS - launch it or assume it's running"""
        print("MelonDS Controller initialized")
        print("Key mappings:")
        print("  A/B/X/Y/L/R/UP/DOWN/LEFT/RIGHT - Direct keys")
        print("  SELECT - Left Shift, START - Right Shift")
        print("  Save State - Shift+F1 (auto), Load State - F1 (startup)")

        # Try to launch MelonDS if configured to do so
        if self.config.get('auto_launch_melonds', True):
            if not self.launch_melonds():
                print("Failed to auto-launch MelonDS")
                print("Please start MelonDS manually with:")
                print(f"  {self.config.get('melonds_path', './melonDS-x86_64.AppImage')} --qwindowgeometry 500x500+250+250 {self.rom_path}")
                input("Press Enter when MelonDS is running with your ROM loaded...")
        else:
            print("Auto-launch disabled. Make sure MelonDS is running with:")
            print(f"  {self.config.get('melonds_path', './melonDS-x86_64.AppImage')} --qwindowgeometry 500x500+250+250 {self.rom_path}")
            input("Press Enter when MelonDS is running with your ROM loaded...")

        # Load state on startup
        try:
            print("Loading state (F1)...")
            self.focus_melonds_window()
            time.sleep(0.5)  # Give it time to focus
            pyautogui.press('f1')
            time.sleep(1)  # Give it time to load
            print("State loaded")
        except Exception as e:
            print(f"Could not load state: {e}")

        # Test if keys are working
        print("\nTesting key input...")
        print("You should see the character move or a menu appear in MelonDS")
        test_input = input("Press Enter to test 'A' key, or type 'skip' to skip test: ")
        if test_input.lower() != 'skip':
            self.execute_move('a')
            print("Did you see the 'A' button press in MelonDS? If not, check window focus.")

        return True
    
    def get_last_move(self):
        """Check database for new moves since last processed"""
        try:
            conn = sqlite3.connect('pump_pokemon.db')
            cursor = conn.execute('SELECT max(id) from game_outcomes')
            
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0]:
                self.last_move_id = result[0]
                print(f"Fetched last id {self.last_move_id}")
            else:
                self.last_move_id = 0

        except Exception as e:
            print(f"Error checking database: {e}")
    
    def database_polling_thread(self):
        """Separate thread for database polling to prevent main loop lag"""
        while self.running:
            try:
                conn = sqlite3.connect('pump_pokemon.db')
                cursor = conn.execute('''
                    SELECT id, winning_move FROM game_outcomes 
                    WHERE id > ? 
                    ORDER BY id ASC 
                    LIMIT 1
                ''', (self.last_move_id,))
                
                result = cursor.fetchone()
                conn.close()
                
                if result:
                    move_id, move = result
                    with self.move_lock:
                        self.last_move_id = move_id
                        self.pending_move = move.lower()
                        print(f"New move from database: {move.lower()} (ID: {move_id})")
                
            except Exception as e:
                print(f"Error in database polling: {e}")
            
            time.sleep(self.config['database_check_interval'])
    
    def state_save_thread_func(self):
        """Separate thread for periodic state saving"""
        while self.running:
            time.sleep(self.config['state_save_interval'])
            if self.running:  # Check again in case we stopped
                try:
                    print("Auto-saving state (Shift+F1)...")
                    self.focus_melonds_window()
                    time.sleep(0.1)  # Small delay after focusing
                    pyautogui.hotkey('shift', 'f1')
                    print("State saved")
                except Exception as e:
                    print(f"Error saving state: {e}")
    
    def focus_melonds_window(self):
        """Try to focus the MelonDS window"""
        try:
            # Try to find MelonDS window by looking for the process
            for proc in psutil.process_iter(['pid', 'name']):
                if 'melonDS' in proc.info['name'] or 'melonds' in proc.info['name'].lower():
                    # Found MelonDS process, try to focus its window
                    # This is a simple approach - click on the center of the screen where MelonDS should be
                    # Based on the geometry 500x500+250+250, center would be around 500, 500
                    pyautogui.click(500, 500)
                    time.sleep(0.1)
                    return True
        except Exception as e:
            print(f"Could not focus MelonDS window: {e}")
        return False

    def get_pending_move(self):
        """Get and clear any pending move from database thread"""
        with self.move_lock:
            move = self.pending_move
            self.pending_move = None
            return move
    
    def execute_move(self, move):
        """Execute a move using pyautogui key presses"""
        # Focus MelonDS window first
        self.focus_melonds_window()

        # Map moves to melonDS keys - using exact key names
        key_map = {
            'a': 'a',
            'b': 'b',
            'x': 'x',
            'y': 'y',
            'l': 'l',
            'r': 'r',
            'up': 'up',
            'down': 'down',
            'left': 'left',
            'right': 'right',
            'select': 'shift',  # Try just 'shift' first
            'start': 'shift'    # We'll handle left/right shift differently
        }

        if move in key_map:
            try:
                print(f"Executing move: {move.upper()}")

                if move == 'select':
                    # Left Shift for SELECT
                    pyautogui.keyDown('shiftleft')
                    time.sleep(self.config['key_press_duration'])
                    pyautogui.keyUp('shiftleft')
                elif move == 'start':
                    # Right Shift for START
                    pyautogui.keyDown('shiftright')
                    time.sleep(self.config['key_press_duration'])
                    pyautogui.keyUp('shiftright')
                else:
                    # Regular keys
                    key = key_map[move]
                    pyautogui.keyDown(key)
                    time.sleep(self.config['key_press_duration'])
                    pyautogui.keyUp(key)

                print(f"Executed move: {move.upper()}")

            except Exception as e:
                print(f"Error executing move {move}: {e}")
        else:
            print(f"Unknown move: {move}")
    
    def run(self):
        """Main control loop"""
        if not self.initialize_melonds():
            return
        
        self.running = True
        
        # Start database polling thread
        self.db_check_thread = threading.Thread(target=self.database_polling_thread, daemon=True)
        self.db_check_thread.start()
        
        # Start state saving thread
        self.state_save_thread = threading.Thread(target=self.state_save_thread_func, daemon=True)
        self.state_save_thread.start()
        
        print("Starting MelonDS controller...")
        print("Checking for moves via database polling thread...")
        print(f"Auto-save interval: {self.config['state_save_interval']} seconds")
        print("Press Ctrl+C to stop")
        
        try:
            while True:
                # Check for pending moves from database thread
                move_from_db = self.get_pending_move()
                if move_from_db:
                    self.execute_move(move_from_db)
                
                # Small sleep to prevent excessive CPU usage
                time.sleep(0.01)  # 10ms sleep
                    
        except KeyboardInterrupt:
            print("\nShutting down MelonDS controller...")
            self.running = False
            try:
                print("Final state save...")
                pyautogui.hotkey('shift', 'f1')
                print("Final state saved")
            except Exception as e:
                print(f"Error saving final state: {e}")

        except Exception as e:
            print(f"Error in control loop: {e}")
        finally:
            self.running = False
            print("MelonDS controller stopped")


if __name__ == "__main__":
    # Import CONFIG from main.py if available
    try:
        import sys
        sys.path.append('.')
        from main import CONFIG
        
        # Add MelonDS-specific config if not present
        melonds_config = CONFIG.copy()
        if 'database_check_interval' not in melonds_config:
            melonds_config.update({
                'database_check_interval': 0.1,  # Check DB every 100ms
                'state_save_interval': 60,  # Save state every 60 seconds
                'key_press_duration': 0.1,  # Hold keys for 100ms
                'auto_launch_melonds': True,
                'melonds_path': './melonDS-x86_64.AppImage',
                'rom_path': 'phg.nds'
            })

        rom_path = melonds_config.get('rom_path', 'phg.nds')
        controller = MelonDSController(rom_path=rom_path, config=melonds_config)
    except ImportError:
        controller = MelonDSController()
    
    controller.run()

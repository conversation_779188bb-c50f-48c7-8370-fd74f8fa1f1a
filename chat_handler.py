import asyncio
import websockets
import json
import time
from collections import defaultdict, Counter
from datetime import datetime
import sqlite3
import threading
import os
from typing import Dict, List, Set

class PumpChatHandler:
    def __init__(self, room_id: str, config: dict):
        self.room_id = room_id
        self.config = config
        self.parsed_commands = []  # Store all valid game commands with metadata
        self.display_commands = []  # Store commands for USER COMMANDS display (doesn't get cleared)
        self.raw_messages = []  # Store all incoming messages for display
        self.mode = "democracy"  # "democracy" or "anarchy"
        self.move_timer = 0
        self.mode_timer = 0
        # Dynamic mode swap interval based on current mode
        self.democracy_duration = config.get('democracy_duration_minutes', 0.5) * 60
        self.anarchy_duration = config.get('anarchy_duration_minutes', 2.0) * 60
        self.democracy_vote_duration = config.get('democracy_vote_duration_seconds', 8.0)
        self.anarchy_move_interval = config.get('move_interval_seconds_anarchy', 0.25)
        self.session_start = datetime.now()
        self.last_moves = []  # Track last 10 moves for display
        self.db_lock = threading.Lock()
        self.init_db()
        
    def init_db(self):
        conn = sqlite3.connect('pump_pokemon.db')
        conn.execute('''
            CREATE TABLE IF NOT EXISTS game_outcomes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME,
                mode TEXT,
                winning_move TEXT,
                vote_count INTEGER,
                user_count INTEGER,
                session_time INTEGER
            )
        ''')

        # Add session_start table to track persistent start time
        conn.execute('''
            CREATE TABLE IF NOT EXISTS session_info (
                id INTEGER PRIMARY KEY,
                start_datetime DATETIME
            )
        ''')

        # Check if we have a start time, if not create one
        cursor = conn.execute('SELECT start_datetime FROM session_info WHERE id = 1')
        result = cursor.fetchone()
        if not result:
            conn.execute('INSERT INTO session_info (id, start_datetime) VALUES (1, ?)',
                        (datetime.now(),))
            self.persistent_start = datetime.now()
        else:
            self.persistent_start = datetime.fromisoformat(result[0])

        conn.commit()
        conn.close()
        
    def save_outcome(self, move: str, votes: int, users: int):
        with self.db_lock:
            conn = sqlite3.connect('pump_pokemon.db')
            session_time = int((datetime.now() - self.session_start).total_seconds())
            conn.execute('''
                INSERT INTO game_outcomes 
                (timestamp, mode, winning_move, vote_count, user_count, session_time)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (datetime.now(), self.mode, move, votes, users, session_time))
            conn.commit()
            conn.close()
    
    
    def parse_game_command(self, message: str) -> str:
        # Nintendo DS controls (includes all Game Boy controls plus DS-specific ones)
        commands = [
            # Basic controls (Game Boy compatible)
            'a', 'b', 'start', 'select', 'up', 'down', 'left', 'right',
            # DS-specific controls
            'x', 'y', 'l', 'r'
        ]
        msg_lower = message.lower().strip()
        for cmd in commands:
            if cmd == msg_lower:
                return cmd
        return None
    
    def swap_mode(self):
        """Swap between democracy and anarchy modes"""
        self.mode = "anarchy" if self.mode == "democracy" else "democracy"
        self.mode_timer = 0  # Reset mode timer
        print(f"Mode automatically changed to: {self.mode}")
    
    def get_current_mode_duration(self):
        """Get the duration for the current mode"""
        return self.democracy_duration if self.mode == "democracy" else self.anarchy_duration

    def process_message(self, event_data: dict):
        if event_data[0] == "newMessage":
            msg_data = event_data[1]
            username = msg_data.get('username', '')
            raw_message = msg_data.get('message', '')
            
            # Store raw messages for display (memory optimized)
            display_time = datetime.now().strftime('%H:%M:%S')
            self.raw_messages.append({
                'username': username,
                'message': raw_message,
                'timestamp': msg_data.get('timestamp', ''),
                'display_time': display_time
            })
            
            # Keep only last 30 raw messages for display (reduced for RAM efficiency)
            if len(self.raw_messages) > 30:
                self.raw_messages.pop(0)

            # Parse and store game commands (DS/Game Boy)
            command = self.parse_game_command(raw_message)
            if command:
                command_data = {
                    'username': username,
                    'command': command,
                    'timestamp': datetime.now(),
                    'display_time': display_time  # Reuse display_time from above
                }
                self.parsed_commands.append(command_data)
                self.display_commands.append(command_data)  # Also add to display list
                
                # Keep only last 30 display commands for UI performance (reduced for RAM)
                if len(self.display_commands) > 30:
                    self.display_commands.pop(0)
                    
                print(f"Valid command: {username} -> {command.upper()}")
    
    def get_winning_move(self):
        if not self.parsed_commands:
            return None
            
        # Check if we're still in the current mode (democracy/anarchy might have ended)
        current_mode_duration = self.get_current_mode_duration()
        if self.mode_timer >= current_mode_duration:
            return None  # Mode is about to swap, don't process moves
            
        if self.mode == "anarchy":
            # In anarchy, return the first command after interval
            if self.move_timer >= self.anarchy_move_interval:
                return self.parsed_commands[0]['command']
        else:
            # In democracy, wait for vote duration and return most popular
            if self.move_timer >= self.democracy_vote_duration:
                # Use Counter for efficiency - single pass counting
                command_counts = Counter(cmd['command'] for cmd in self.parsed_commands)
                return command_counts.most_common(1)[0][0]
        return None
    
    def execute_move(self, move: str):
        # Efficient counting with single pass
        vote_count = 0
        users = set()
        for cmd in self.parsed_commands:
            if cmd['command'] == move:
                vote_count += 1
            users.add(cmd['username'])
        unique_users = len(users)
        
        print(f"EXECUTE: {move.upper()} (votes: {vote_count}, users: {unique_users})")
        
        # Find the user who made the winning move (for anarchy mode)
        winning_user = 'by vote'
        if self.mode == 'anarchy':
            for cmd in self.parsed_commands:
                if cmd['command'] == move:
                    winning_user = cmd['username']
                    break
        
        # Track last moves for display
        move_data = {
            'move': move.upper(),
            'mode': self.mode,
            'user': winning_user,
            'votes': vote_count,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        }
        self.last_moves.append(move_data)
        if len(self.last_moves) > 10:
            self.last_moves.pop(0)
        
        # Save outcome to database
        self.save_outcome(move, vote_count, unique_users)
        
        # Clear command memory as specified
        self.parsed_commands.clear()
        self.move_timer = 0
        
        return move.upper()
    
    def get_stats(self):
        now = datetime.now()  # Single time calculation
        session_time = now - self.session_start
        persistent_session_time = now - self.persistent_start
        
        # Efficient single-pass counting
        current_votes = Counter()
        users = set()
        for cmd in self.parsed_commands:
            current_votes[cmd['command']] += 1
            users.add(cmd['username'])
        unique_users = len(users)
        
        return {
            'mode': self.mode,
            'current_votes': dict(current_votes),
            'user_count': unique_users,
            'session_time': str(session_time).split('.')[0],
            'persistent_session_time': str(persistent_session_time).split('.')[0],
            'move_timer': self.move_timer,
            'mode_timer': self.mode_timer,
            'mode_swap_interval': self.get_current_mode_duration(),
            'total_messages': len(self.raw_messages),
            'last_moves': self.last_moves,
            'all_messages': self.raw_messages,
            'parsed_messages': [{
                'username': cmd['username'],
                'move': cmd['command'].upper(),
                'timestamp': cmd['display_time']
            } for cmd in self.display_commands]
        }

    async def connect_and_listen(self):
        ws_url = "wss://livechat.pump.fun/socket.io/?EIO=4&transport=websocket"
        
        async with websockets.connect(
            ws_url,
            additional_headers={
                "Origin": "https://pump.fun",
                "User-Agent": "Mozilla/5.0",
            }
        ) as ws:
            print("Connected to pump.fun chat")
            
            # Timer for move intervals and mode swaps
            last_move_check = time.time()
            last_mode_check = time.time()
            
            async for msg in ws:
                # Handle ping/pong
                if msg == "2":
                    await ws.send("3")
                    continue
                
                # Initial handshake
                if msg.startswith("0"):
                    connect_payload = {
                        "origin": "https://pump.fun",
                        "timestamp": int(time.time() * 1000),
                        "token": None,
                    }
                    await ws.send("40" + json.dumps(connect_payload))
                
                # Join room
                if msg.startswith("40"):
                    join_payload = [
                        "joinRoom",
                        {"roomId": self.room_id, "username": ""}
                    ]
                    await ws.send("42" + json.dumps(join_payload))
                
                # Process messages
                if msg.startswith("42"):
                    try:
                        data = json.loads(msg[2:])
                        self.process_message(data)
                    except Exception as e:
                        print(f"Parse error: {e}")
                
                # Update timers
                current_time = time.time()
                self.move_timer = int(current_time - last_move_check)
                self.mode_timer = int(current_time - last_mode_check)
                
                # Check for mode swap based on current mode duration
                current_mode_duration = self.get_current_mode_duration()
                if self.mode_timer >= current_mode_duration:
                    self.swap_mode()
                    last_mode_check = current_time
                
                # Check for moves based on timer
                winning_move = self.get_winning_move()
                if winning_move:
                    self.execute_move(winning_move)
                    last_move_check = current_time
#!/usr/bin/env python3
"""
DeSmuME Controller for Pump Plays Pokemon

This module provides a controller class that uses py-desmume to emulate
Nintendo DS games, replacing the previous MelonDS + pyautogui approach.
"""
import os
import time
import sqlite3
import threading
from typing import Optional, Dict, Any

from desmume.emulator import <PERSON>Smu<PERSON>
from desmume.controls import keymask, Keys


class DeSmuMEController:
    """
    Controller for DeSmuME emulator using py-desmume library.
    
    This class handles:
    - Emulator initialization and ROM loading
    - Input processing and key mapping
    - State save/load functionality
    - Database polling for new moves
    - Performance optimization for smooth gameplay
    """
    
    def __init__(self, rom_path: str = 'phg.nds', config: Optional[Dict[str, Any]] = None):
        """
        Initialize the DeSmuME controller.
        
        Args:
            rom_path: Path to the Nintendo DS ROM file
            config: Configuration dictionary with various settings
        """
        self.rom_path = rom_path
        self.last_move_id = 0  # Track last processed move from database
        self.config = config or {
            'database_check_interval': 0.1,  # Check database every 100ms
            'state_save_interval': 60,  # Save state every 60 seconds
            'key_press_duration': 0.1,  # How long to hold keys (100ms)
            'auto_load_state': True,  # Auto-load state on startup
            'framerate_limit': 60,  # Target framerate
            'audio_enabled': True,  # Enable audio
            'audio_volume': 50,  # Audio volume (0-100)
            'state_file': 'desmume_state.dst',  # Default state file
            'skip_frame_limit': 0,  # Frame skip for performance (0 = no skip)
            'cpu_mode': 'jit',  # CPU mode: 'interpreter' or 'jit'
            'threaded_3d': True,  # Enable threaded 3D rendering
            'opengl_enabled': True,  # Use OpenGL if available
        }
        
        # Threading for database polling to prevent lag
        self.db_check_thread = None
        self.state_save_thread = None
        self.running = False
        self.pending_move = None
        self.move_lock = threading.Lock()
        
        # DeSmuME emulator instance
        self.emu = None
        
        # Key mapping from move names to DeSmuME keys
        self.key_map = {
            'a': Keys.KEY_A,
            'b': Keys.KEY_B,
            'x': Keys.KEY_X,
            'y': Keys.KEY_Y,
            'l': Keys.KEY_L,
            'r': Keys.KEY_R,
            'up': Keys.KEY_UP,
            'down': Keys.KEY_DOWN,
            'left': Keys.KEY_LEFT,
            'right': Keys.KEY_RIGHT,
            'select': Keys.KEY_SELECT,
            'start': Keys.KEY_START,
        }
        
        # Initialize last move tracking
        self.get_last_move()
    
    def initialize_emulator(self) -> bool:
        """
        Initialize the DeSmuME emulator and load the ROM.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        try:
            print("Initializing DeSmuME emulator...")

            # Create emulator instance
            self.emu = DeSmuME()

            # Configure performance settings
            self._configure_performance_settings()

            # Configure audio settings
            self._configure_audio_settings()

            # Load ROM
            if not os.path.exists(self.rom_path):
                print(f"ROM file not found: {self.rom_path}")
                return False

            print(f"Loading ROM: {self.rom_path}")
            self.emu.open(self.rom_path, auto_resume=False)

            # Load state on startup if configured
            if self.config.get('auto_load_state', True):
                self.load_state_on_startup()

            print("DeSmuME emulator initialized successfully")
            print("Key mappings:")
            print("  A/B/X/Y/L/R/UP/DOWN/LEFT/RIGHT/SELECT/START - Direct controls")
            print(f"Performance settings:")
            print(f"  Framerate limit: {self.config.get('framerate_limit', 60)} FPS")
            print(f"  Audio enabled: {self.config.get('audio_enabled', True)}")
            print(f"  OpenGL enabled: {self.config.get('opengl_enabled', True)}")

            return True

        except Exception as e:
            print(f"Error initializing DeSmuME: {e}")
            return False

    def _configure_performance_settings(self):
        """Configure performance-related settings for optimal gameplay."""
        try:
            # Note: Some settings may not be available in py-desmume API
            # These are the settings we can configure

            # Frame skip for performance (if needed)
            skip_frames = self.config.get('skip_frame_limit', 0)
            if skip_frames > 0:
                print(f"Frame skip configured: {skip_frames}")
                # Note: Frame skipping would be handled in the main loop

            print("Performance settings configured")

        except Exception as e:
            print(f"Warning: Could not configure all performance settings: {e}")

    def _configure_audio_settings(self):
        """Configure audio settings."""
        try:
            if not self.config.get('audio_enabled', True):
                self.emu.volume_set(0)
                print("Audio disabled")
            else:
                volume = self.config.get('audio_volume', 50)
                self.emu.volume_set(volume)
                print(f"Audio volume set to {volume}%")

        except Exception as e:
            print(f"Warning: Could not configure audio settings: {e}")
    
    def load_state_on_startup(self):
        """Load saved state on startup if it exists."""
        state_file = self.config.get('state_file', 'desmume_state.dst')
        
        try:
            if os.path.exists(state_file):
                print(f"Loading state from {state_file}...")
                self.emu.savestate.load_file(state_file)
                print("State loaded successfully")
            else:
                print(f"No existing state file found at {state_file}")
        except Exception as e:
            print(f"Could not load state: {e}")
    
    def save_state(self):
        """Save current emulator state to file."""
        state_file = self.config.get('state_file', 'desmume_state.dst')
        
        try:
            print(f"Saving state to {state_file}...")
            self.emu.savestate.save_file(state_file)
            print("State saved successfully")
        except Exception as e:
            print(f"Error saving state: {e}")
    
    def get_last_move(self):
        """Check database for the last processed move ID."""
        try:
            conn = sqlite3.connect('pump_pokemon.db')
            cursor = conn.execute('SELECT max(id) from game_outcomes')
            
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0]:
                self.last_move_id = result[0]
                print(f"Fetched last move ID: {self.last_move_id}")
            else:
                self.last_move_id = 0
                
        except Exception as e:
            print(f"Error checking database: {e}")
    
    def database_polling_thread(self):
        """Separate thread for database polling to prevent main loop lag."""
        while self.running:
            try:
                conn = sqlite3.connect('pump_pokemon.db')
                cursor = conn.execute('''
                    SELECT id, winning_move FROM game_outcomes 
                    WHERE id > ? 
                    ORDER BY id ASC 
                    LIMIT 1
                ''', (self.last_move_id,))
                
                result = cursor.fetchone()
                conn.close()
                
                if result:
                    move_id, move = result
                    with self.move_lock:
                        self.last_move_id = move_id
                        self.pending_move = move.lower()
                        print(f"New move from database: {move.lower()} (ID: {move_id})")
                
            except Exception as e:
                print(f"Error in database polling: {e}")
            
            time.sleep(self.config['database_check_interval'])
    
    def state_save_thread_func(self):
        """Separate thread for periodic state saving."""
        while self.running:
            time.sleep(self.config['state_save_interval'])
            if self.running:  # Check again in case we stopped
                self.save_state()
    
    def get_pending_move(self) -> Optional[str]:
        """Get and clear any pending move from database thread."""
        with self.move_lock:
            move = self.pending_move
            self.pending_move = None
            return move
    
    def execute_move(self, move: str):
        """
        Execute a move using DeSmuME input system.
        
        Args:
            move: The move name (e.g., 'a', 'up', 'start')
        """
        if not self.emu:
            print("Emulator not initialized")
            return
        
        move = move.lower()
        if move not in self.key_map:
            print(f"Unknown move: {move}")
            return
        
        try:
            print(f"Executing move: {move.upper()}")
            
            # Get the key mask for the move
            key = self.key_map[move]
            key_mask = keymask(key)
            
            # Press the key
            self.emu.input.keypad_add_key(key_mask)
            
            # Hold the key for the configured duration
            time.sleep(self.config['key_press_duration'])
            
            # Release the key
            self.emu.input.keypad_rm_key(key_mask)
            
            print(f"Executed move: {move.upper()}")
            
        except Exception as e:
            print(f"Error executing move {move}: {e}")

    def run(self):
        """Main control loop for the emulator."""
        if not self.initialize_emulator():
            print("Failed to initialize emulator")
            return

        self.running = True

        # Start database polling thread
        self.db_check_thread = threading.Thread(target=self.database_polling_thread, daemon=True)
        self.db_check_thread.start()

        # Start state saving thread
        self.state_save_thread = threading.Thread(target=self.state_save_thread_func, daemon=True)
        self.state_save_thread.start()

        print("Starting DeSmuME controller...")
        print("Checking for moves via database polling thread...")
        print(f"Auto-save interval: {self.config['state_save_interval']} seconds")
        print("Press Ctrl+C to stop")

        # Resume emulator
        self.emu.resume()

        try:
            frame_time = 1.0 / self.config.get('framerate_limit', 60)
            skip_frame_limit = self.config.get('skip_frame_limit', 0)
            frame_count = 0

            while True:
                frame_start = time.time()

                # Check for pending moves from database thread
                move_from_db = self.get_pending_move()
                if move_from_db:
                    self.execute_move(move_from_db)

                # Frame skipping for performance
                should_skip_frame = (skip_frame_limit > 0 and
                                   frame_count % (skip_frame_limit + 1) != 0)

                if should_skip_frame:
                    self.emu.skip_next_frame()

                # Run one emulator cycle/frame
                self.emu.cycle(with_joystick=False)

                frame_count += 1

                # Frame rate limiting (only if not skipping frames)
                if not should_skip_frame:
                    frame_elapsed = time.time() - frame_start
                    if frame_elapsed < frame_time:
                        time.sleep(frame_time - frame_elapsed)
                else:
                    # Minimal sleep for skipped frames to prevent CPU overload
                    time.sleep(0.001)

        except KeyboardInterrupt:
            print("\nShutting down DeSmuME controller...")
            self.running = False
            try:
                print("Final state save...")
                self.save_state()
                print("Final state saved")
            except Exception as e:
                print(f"Error saving final state: {e}")

        except Exception as e:
            print(f"Error in control loop: {e}")
        finally:
            self.running = False
            if self.emu:
                self.emu.destroy()
            print("DeSmuME controller stopped")

    def cleanup(self):
        """Clean up resources."""
        self.running = False
        if self.emu:
            try:
                self.save_state()
            except Exception as e:
                print(f"Error during cleanup save: {e}")
            self.emu.destroy()
            self.emu = None


if __name__ == "__main__":
    # Import CONFIG from main.py if available
    try:
        import sys
        sys.path.append('.')
        from main import CONFIG

        # Add DeSmuME-specific config if not present
        desmume_config = CONFIG.copy()
        desmume_config.update({
            'database_check_interval': 0.1,  # Check DB every 100ms
            'state_save_interval': 60,  # Save state every 60 seconds
            'key_press_duration': 0.1,  # Hold keys for 100ms
            'auto_load_state': True,
            'framerate_limit': 60,  # Target 60 FPS for smooth gameplay
            'audio_enabled': True,
            'audio_volume': 50,  # 50% volume to avoid audio issues
            'state_file': 'desmume_state.dst',
            'skip_frame_limit': 0,  # No frame skipping by default
            'cpu_mode': 'jit',
            'threaded_3d': True,
            'opengl_enabled': True,
        })

        rom_path = desmume_config.get('rom_path', 'phg.nds')
        controller = DeSmuMEController(rom_path=rom_path, config=desmume_config)
    except ImportError:
        controller = DeSmuMEController()

    controller.run()

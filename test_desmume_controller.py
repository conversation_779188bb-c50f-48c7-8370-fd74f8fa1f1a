#!/usr/bin/env python3
"""
Test script for DeSmuME controller functionality
"""
import os
import time
import sys
from desmume_controller import DeSmuMEController

def test_controller_initialization():
    """Test if the controller can be initialized"""
    try:
        config = {
            'database_check_interval': 0.1,
            'state_save_interval': 60,
            'key_press_duration': 0.05,  # Shorter for testing
            'auto_load_state': False,  # Don't auto-load for testing
            'framerate_limit': 60,
            'audio_enabled': False,  # Disable audio for testing
            'state_file': 'test_state.dst',
        }
        
        controller = DeSmuMEController(rom_path='phg.nds', config=config)
        
        if controller.initialize_emulator():
            print("✓ Controller initialized successfully")
            
            # Test input mapping
            test_moves = ['a', 'b', 'up', 'down', 'left', 'right', 'start', 'select']
            
            for move in test_moves:
                if move in controller.key_map:
                    print(f"✓ Move '{move}' mapped to key {controller.key_map[move]}")
                else:
                    print(f"✗ Move '{move}' not found in key mapping")
                    return False
            
            # Test actual input execution (very brief)
            print("Testing input execution...")
            for move in ['a', 'up']:
                controller.execute_move(move)
                time.sleep(0.1)  # Brief pause between inputs
            
            print("✓ Input execution test completed")
            
            # Test state save/load
            try:
                controller.save_state()
                print("✓ State save test passed")
                
                # Clean up test state file
                if os.path.exists(controller.config['state_file']):
                    os.remove(controller.config['state_file'])
                    
            except Exception as e:
                print(f"⚠ State save test failed: {e}")
            
            # Clean up
            controller.cleanup()
            return True
        else:
            print("✗ Controller initialization failed")
            return False
            
    except Exception as e:
        print(f"✗ Error during controller test: {e}")
        return False

def test_key_mapping_completeness():
    """Test that all expected keys are mapped"""
    controller = DeSmuMEController()
    
    expected_keys = [
        'a', 'b', 'x', 'y', 'l', 'r',
        'up', 'down', 'left', 'right',
        'select', 'start'
    ]
    
    missing_keys = []
    for key in expected_keys:
        if key not in controller.key_map:
            missing_keys.append(key)
    
    if missing_keys:
        print(f"✗ Missing key mappings: {missing_keys}")
        return False
    else:
        print("✓ All expected keys are mapped")
        return True

def test_database_integration():
    """Test database integration without actually running the emulator"""
    try:
        controller = DeSmuMEController()
        
        # Test getting last move (should work even without database)
        controller.get_last_move()
        print("✓ Database integration test passed")
        
        # Test pending move functionality
        controller.pending_move = "test_move"
        move = controller.get_pending_move()
        if move == "test_move":
            print("✓ Pending move functionality works")
            return True
        else:
            print("✗ Pending move functionality failed")
            return False
            
    except Exception as e:
        print(f"✗ Database integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing DeSmuME Controller...")
    print("=" * 50)
    
    tests = [
        test_key_mapping_completeness,
        test_database_integration,
        test_controller_initialization,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
        print()
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! DeSmuME controller is ready to use.")
        return True
    else:
        print("✗ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
